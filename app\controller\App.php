<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\Request;
use app\model\App as AppModel;
use app\validate\App as AppValidate;
use app\service\YmService;
use think\exception\ValidateException;

/**
 * App控制器
 */
class App extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\\middleware\\Auth',
        'app\\middleware\\AppCheck' => ['except' => ['index', 'create', 'save']],
    ];

    /**
     * App列表
     */
    public function index(Request $request)
    {
        // 获取搜索参数
        $search = [
            'title' => $request->param('title', ''),
            'appCode' => $request->param('appCode', ''),
            'isOnline' => $request->param('isOnline', ''),
            'isPrivate' => $request->param('isPrivate', ''),
        ];

        // 查询App列表
        $list = AppModel::withSearch(['title', 'appCode', 'isOnline', 'isPrivate'], $search)
            ->order('createTime', 'desc')
            ->paginate([
                'list_rows' => 12,
                'query' => $request->param(),
            ]);

        // 模板赋值
        View::assign([
            'list' => $list,
            'search' => $search,
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 创建App页面
     */
    public function create()
    {
        // 设置为创建模式
        View::assign([
            'isEdit' => false,
        ]);

        // 渲染合并后的表单模板
        return View::fetch('form');
    }

    /**
     * 保存App
     */
    public function save(Request $request)
    {
        // 获取参数
        $data = $request->post();

        // 添加用户ID
        $data['salerUserId'] = $request->userId;

        // 验证数据
        try {
            validate(AppValidate::class)
                ->scene('create')
                ->check($data);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
        // 如果App上线，设置状态为审核中
        if($data['isOnline']){
            $data['status'] = 1;
        }
        // 创建App
        $app = new AppModel();
        $app->save($data);

        // 返回结果
        return $this->success('创建成功', ['appId' => $app->appId]);
    }

    /**
     * App详情
     */
    public function detail(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境列表
        $runtimes = $app->runtimes()->select()->toArray();

        // 获取YmService配置
        $ymConfig = YmService::getConfig();

        // 提取运行时环境名称和图标
        $runtimeNames = [];
        $runtimeIcons = [];

        // 遍历配置中的运行时环境
        foreach ($ymConfig['runtime'] as $runtimeGroup) {
            foreach ($runtimeGroup['runtime'] as $runtime) {
                $runtimeNames[$runtime['id']] = $runtime['label'];
                $runtimeIcons[$runtime['id']] = $runtime['icon'];
            }
        }

        // 模板赋值
        View::assign([
            'app' => $app,
            'runtimes' => $runtimes,
            'runtimeCount'=>count($runtimes),
            'runtimeNames' => $runtimeNames,
            'runtimeIcons' => $runtimeIcons,
            'ymConfig' => $ymConfig,
            'active' => 'detail',
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 编辑App页面
     */
    public function edit(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 模板赋值
        View::assign([
            'isEdit' => true,
            'app' => $app,
        ]);

        // 渲染合并后的表单模板
        return View::fetch('form');
    }

    /**
     * 更新App
     */
    public function update(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取参数
        $data = $request->post();

        // 验证数据
        try {
            validate(AppValidate::class)
                ->scene('edit')
                ->check($data);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
        // 如果App上线，设置状态为审核中
        if($data['isOnline']){
            $data['status'] = 1;
        }
        // 更新App
        $app->save($data);

        // 返回结果
        return $this->success('更新成功');
    }

    /**
     * 删除App
     */
    public function delete(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 删除App
        $app->delete();

        // 返回结果
        return $this->success('删除成功');
    }
}
