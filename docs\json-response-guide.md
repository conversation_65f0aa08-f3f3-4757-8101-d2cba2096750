# JSON 响应统一封装使用指南

## 概述

为了统一项目中的 JSON 响应格式，我们在 `BaseController` 中封装了三个统一的 JSON 返回方法：

- `success()` - 返回成功响应
- `error()` - 返回错误响应  
- `jsonResponse()` - 通用 JSON 响应

## 响应格式

所有 JSON 响应都遵循统一格式：

```json
{
    "code": 1,           // 状态码：1=成功，0=失败
    "msg": "操作成功",    // 响应消息
    "data": {}           // 返回数据（可选）
}
```

## 方法说明

### 1. success() 方法

用于返回成功的响应。

**方法签名：**
```php
protected function success(string $msg = '操作成功', array $data = [], int $code = 1)
```

**参数说明：**
- `$msg`: 成功消息，默认为 "操作成功"
- `$data`: 返回的数据数组，默认为空数组
- `$code`: 状态码，默认为 1

**使用示例：**
```php
// 简单成功响应
return $this->success('创建成功');

// 带数据的成功响应
return $this->success('创建成功', ['appId' => $app->appId]);

// 自定义状态码
return $this->success('操作完成', [], 2);
```

### 2. error() 方法

用于返回错误的响应。

**方法签名：**
```php
protected function error(string $msg = '操作失败', array $data = [], int $code = 0)
```

**参数说明：**
- `$msg`: 错误消息，默认为 "操作失败"
- `$data`: 返回的数据数组，默认为空数组
- `$code`: 状态码，默认为 0

**使用示例：**
```php
// 简单错误响应
return $this->error('参数错误');

// 验证错误响应
return $this->error($e->getMessage());

// 带数据的错误响应
return $this->error('删除失败', ['failedIds' => [1, 2, 3]]);
```

### 3. jsonResponse() 方法

通用的 JSON 响应方法，适用于需要自定义状态码的场景。

**方法签名：**
```php
protected function jsonResponse(int $code, string $msg, array $data = [])
```

**参数说明：**
- `$code`: 状态码
- `$msg`: 响应消息
- `$data`: 返回的数据数组，默认为空数组

**使用示例：**
```php
// 自定义状态码响应
return $this->jsonResponse(2, '审核中', ['status' => 'pending']);
```

## 迁移指南

### 旧的写法：
```php
// 成功响应
return json(['code' => 1, 'msg' => '创建成功', 'data' => ['appId' => $app->appId]]);

// 错误响应
return json(['code' => 0, 'msg' => $e->getMessage()]);
```

### 新的写法：
```php
// 成功响应
return $this->success('创建成功', ['appId' => $app->appId]);

// 错误响应
return $this->error($e->getMessage());
```

## 优势

1. **代码简洁**：减少重复代码，提高开发效率
2. **格式统一**：确保所有 JSON 响应格式一致
3. **易于维护**：统一修改响应格式只需修改基类方法
4. **类型安全**：方法参数有明确的类型定义
5. **向后兼容**：保持原有的响应格式不变

## 注意事项

1. 所有控制器都应该继承 `BaseController`
2. 优先使用 `success()` 和 `error()` 方法
3. 只有在需要特殊状态码时才使用 `jsonResponse()` 方法
4. 保持响应消息的一致性和用户友好性
