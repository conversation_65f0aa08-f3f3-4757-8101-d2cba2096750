<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\View;
use think\Request;
use app\model\AppRuntime as AppRuntimeModel;
use app\validate\AppRuntime as AppRuntimeValidate;
use app\service\YmService;
use think\exception\ValidateException;

/**
 * AppRuntime控制器
 */
class AppRuntime extends BaseController
{
    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [
        'app\\middleware\\Auth',
        'app\\middleware\\AppCheck',
        'app\\middleware\\AppRuntimeCheck' => ['only' => ['edit', 'update', 'detail']],
    ];

    /**
     * 编辑运行时环境页面
     */
    public function edit(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境ID
        $runtimeId = $request->param('runtimeId');

        // 获取AppRuntime信息
        $appRuntime = $request->appRuntime;

        // 获取当前选项卡
        $tab = $request->param('tab', 'config');

        // 如果选择了发布列表选项卡，重定向到发布列表页面
        if ($tab === 'release') {
            return redirect(url('appRelease/index', ['appId' => $app->appId, 'runtimeId' => $runtimeId]));
        }
        $has = true;
        // 如果AppRuntime不存在，创建一个默认值
        if (!$appRuntime) {
            $has = false;
            $appRuntime = [
                'appId' => $app->appId,
                'runtimeId' => $runtimeId,
                'requiredPluginIds' => '',
                'configSchema' => [],
                'extra' => [],
                'demoLink' => '',
                'content' => '',
                'isOnline' => 0,
                'status' => 0,
            ];
        }

        // 获取YmService配置
        $ymConfig = YmService::getConfig();
        $ymPlugins = YmService::getPlguns();

        // 提取运行时环境名称和图标
        $runtimeNames = [];
        $runtimeIcons = [];
        $pluginKey = '';

        // 遍历配置中的运行时环境
        foreach ($ymConfig['runtime'] as $runtimeGroup) {
            foreach ($runtimeGroup['runtime'] as $runtime) {
                $runtimeNames[$runtime['id']] = $runtime['label'];
                $runtimeIcons[$runtime['id']] = $runtime['icon'];

                // 获取当前环境的pluginKey
                if ($runtime['id'] == $runtimeId) {
                    $pluginKey = $runtime['pluginKey'];
                }
            }
        }

        $runtimeName = $runtimeNames[$runtimeId] ?? '未知环境';

        // 获取已选择的插件ID列表
        $selectedPluginIds = [];
        if (!empty($appRuntime['requiredPluginIds'])) {
            $selectedPluginIds = explode(',', $appRuntime['requiredPluginIds']);
        }

        // 模板赋值
        View::assign([
            'app' => $app,
            'has'=>$has,
            'appRuntime' => $appRuntime,
            'runtimeId' => $runtimeId,
            'runtimeName' => $runtimeName,
            'runtimeIcon' => $runtimeIcons[$runtimeId] ?? '',
            'pluginKey' => $pluginKey,
            'ymPlugins' => $ymPlugins,
            'selectedPluginIds' => $selectedPluginIds,
            'active' => 'runtime',
            'tab' => $tab,
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 查看运行时环境详情
     */
    public function detail(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境ID
        $runtimeId = $request->param('runtimeId');

        // 获取AppRuntime信息
        $appRuntime = $request->appRuntime;

        // 如果AppRuntime不存在，创建一个默认值
        if (!$appRuntime) {
            $appRuntime = [
                'appId' => $app->appId,
                'runtimeId' => $runtimeId,
                'requiredPluginIds' => '',
                'configSchema' => [],
                'extra' => [],
                'demoLink' => '',
                'content' => '',
                'isOnline' => 0,
                'status' => 0,
            ];
        }

        // 获取YmService配置
        $ymConfig = YmService::getConfig();
        $ymPlugins = YmService::getPlguns();

        // 提取运行时环境名称和图标
        $runtimeNames = [];
        $runtimeIcons = [];
        $pluginKey = '';

        // 遍历配置中的运行时环境
        foreach ($ymConfig['runtime'] as $runtimeGroup) {
            foreach ($runtimeGroup['runtime'] as $runtime) {
                $runtimeNames[$runtime['id']] = $runtime['label'];
                $runtimeIcons[$runtime['id']] = $runtime['icon'];

                // 获取当前环境的pluginKey
                if ($runtime['id'] == $runtimeId) {
                    $pluginKey = $runtime['pluginKey'];
                }
            }
        }

        $runtimeName = $runtimeNames[$runtimeId] ?? '未知环境';

        // 获取已选择的插件ID列表
        $selectedPluginIds = [];
        $selectedPlugins = [];

        if (!empty($appRuntime['requiredPluginIds'])) {
            $selectedPluginIds = explode(',', $appRuntime['requiredPluginIds']);

            // 获取已选择的插件详情
            foreach ($ymPlugins['plugins'] as $plugin) {
                if (in_array($plugin['id'], $selectedPluginIds)) {
                    $selectedPlugins[] = $plugin;
                }
            }
        }

        // 模板赋值
        View::assign([
            'app' => $app,
            'appRuntime' => $appRuntime,
            'runtimeId' => $runtimeId,
            'runtimeName' => $runtimeName,
            'runtimeIcon' => $runtimeIcons[$runtimeId] ?? '',
            'pluginKey' => $pluginKey,
            'ymPlugins' => $ymPlugins,
            'selectedPluginIds' => $selectedPluginIds,
            'selectedPlugins' => $selectedPlugins,
            'active' => 'runtime',
        ]);

        // 渲染模板
        return View::fetch();
    }

    /**
     * 更新运行时环境
     */
    public function update(Request $request)
    {
        // 获取App信息
        $app = $request->app;

        // 获取运行时环境ID
        $runtimeId = $request->param('runtimeId');

        // 获取AppRuntime信息
        $appRuntime = $request->appRuntime;

        // 获取参数
        $data = $request->post();
        $data['appId'] = $app->appId;
        $data['runtimeId'] = $runtimeId;

        // 验证数据
        try {
            validate(AppRuntimeValidate::class)
                ->scene('edit')
                ->check($data);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        // 如果AppRuntime不存在，创建一个新的
        if (!$appRuntime) {
            $appRuntime = new AppRuntimeModel();
        }
        if($data['isOnline']){
            $data['status'] = 1;
        }
        // 更新AppRuntime
        $appRuntime->save($data);

        // 返回结果
        return $this->success('更新成功');
    }
}
