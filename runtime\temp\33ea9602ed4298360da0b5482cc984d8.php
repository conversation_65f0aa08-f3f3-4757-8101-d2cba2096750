<?php /*a:4:{s:40:"E:\Ym\marketAdmin\app\view\app\form.html";i:1748514033;s:43:"E:\Ym\marketAdmin\app\view\common\base.html";i:1747638572;s:45:"E:\Ym\marketAdmin\app\view\common\header.html";i:1747708602;s:45:"E:\Ym\marketAdmin\app\view\common\footer.html";i:1747638354;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title><?php echo !empty($isEdit) ? '编辑'  :  '创建'; ?>应用 - 应用市场管理系统</title>
    <script src="/static/js/tw.js"></script>
    <link rel="stylesheet" href="/static/css/iconfont.css">
    <style>
        /* 基础响应式样式 */
        html, body {
            overflow-x: hidden;
            width: 100%;
            position: relative;
        }
        /* 滚动条样式优化 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 表格响应式样式 */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        /* 移动端触摸优化 */
        @media (max-width: 768px) {
            .touch-action-manipulation {
                touch-action: manipulation;
            }
            .tap-highlight-transparent {
                -webkit-tap-highlight-color: transparent;
            }
        }
    </style>
    
<!-- form-create设计器依赖 -->
<link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
<style>
/* 表单设计器弹窗样式 */
.form-designer-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.form-designer-modal.active {
    opacity: 1;
    visibility: visible;
}

.form-designer-container {
    width: 90%;
    height: 90%;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-designer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.form-designer-body {
    flex: 1;
    overflow: auto;
    position: relative;
}

.form-designer-footer {
    padding: 12px 16px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    background-color: #f9fafb;
}

/* 修复form-create设计器与tailwind的样式冲突 */
#form-designer-container [class^="el-"] {
    box-sizing: border-box;
}

#form-designer-container .el-button {
    margin: 0;
}
</style>

</head>
<body class="bg-gray-100 min-h-screen touch-action-manipulation tap-highlight-transparent">
    <!-- 移动端菜单按钮 -->
    <div id="mobile-menu-button" class="fixed z-50 bottom-4 right-4 md:hidden bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
        <i class="iconfont icon-menu text-xl"></i>
    </div>

    <!-- 头部 -->
    

    <!-- 主体内容 -->
    <div class="container mx-auto px-4 py-6">
        
<div class="mb-6">
    <div class="flex items-center justify-between mb-4">
        <h1 class="text-2xl font-bold text-gray-800 flex items-center">
            <i class="iconfont icon-app text-indigo-600 mr-2"></i><?php echo !empty($isEdit) ? '编辑'  :  '创建'; ?>应用
        </h1>
        <a href="<?php echo url('App/index'); ?>" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 flex items-center">
            <i class="iconfont icon-back mr-1"></i> 返回列表
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
        <form id="appForm" data-validate>
            <!-- 基本信息 -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                    <i class="iconfont icon-info-circle text-indigo-500 mr-2"></i>基本信息
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="group">
                        <label for="appCode" class="block text-xs font-medium text-gray-700 mb-1">
                            应用唯一标识 <?php if(!$isEdit): ?><span class="text-red-500">*</span><?php endif; ?>
                        </label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 pl-2.5 flex items-center text-gray-400 pointer-events-none">
                                <i class="iconfont icon-code text-xs"></i>
                            </span>
                            <input type="text" id="appCode" name="appCode" <?php if($isEdit): ?>value="<?php echo htmlentities((string) $app['appCode']); ?>" disabled<?php else: ?>required<?php endif; ?>
                                class="w-full pl-8 pr-3 py-1.5 text-sm rounded-md border border-gray-300 shadow-sm
                                <?php if($isEdit): ?>bg-gray-100<?php else: ?>focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                hover:border-indigo-300 transition-colors duration-200<?php endif; ?>">
                        </div>
                        <p class="mt-1 text-xs text-gray-500">应用的唯一标识，<?php if($isEdit): ?>不可修改<?php else: ?>创建后不可修改<?php endif; ?></p>
                    </div>

                    <div class="group">
                        <label for="title" class="block text-xs font-medium text-gray-700 mb-1">
                            应用标题 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 pl-2.5 flex items-center text-gray-400 pointer-events-none">
                                <i class="iconfont icon-title text-xs"></i>
                            </span>
                            <input type="text" id="title" name="title" <?php if($isEdit): ?>value="<?php echo htmlentities((string) $app['title']); ?>"<?php endif; ?> required
                                class="w-full pl-8 pr-3 py-1.5 text-sm rounded-md border border-gray-300 shadow-sm
                                focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                hover:border-indigo-300 transition-colors duration-200">
                        </div>
                    </div>

                    <div class="md:col-span-2">
                        <label for="description" class="block text-xs font-medium text-gray-700 mb-1">
                            应用说明 <span class="text-red-500">*</span>
                        </label>
                        <textarea id="description" name="description" rows="3" required
                            class="w-full px-3 py-1.5 text-sm rounded-md border border-gray-300 shadow-sm
                            focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                            hover:border-indigo-300 transition-colors duration-200"><?php if($isEdit): ?><?php echo htmlentities((string) $app['description']); ?><?php endif; ?></textarea>
                    </div>

                    <div class="group">
                        <label class="block text-xs font-medium text-gray-700 mb-1">
                            应用徽标 <span class="text-red-500">*</span>
                        </label>
                        <div class="logo-uploader">
                            <input type="hidden" id="logo" name="logo" <?php if($isEdit): ?>value="<?php echo htmlentities((string) $app['logo']); ?>"<?php endif; ?> required>
                            <div id="logoPreview" class="logo-preview flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 hover:border-indigo-400 transition-colors duration-200 bg-gray-50 cursor-pointer overflow-hidden h-32 w-32">
                                <div class="logo-empty flex flex-col items-center justify-center p-4 h-full w-full <?php if($isEdit && $app['logo']): ?>hidden<?php endif; ?>">
                                    <i class="iconfont icon-image text-2xl text-gray-400 mb-1"></i>
                                    <p class="text-xs text-gray-500 text-center">点击或拖拽上传</p>
                                    <p class="text-xs text-gray-400 mt-0.5">200×200px</p>
                                </div>
                                <div class="logo-image <?php if(!$isEdit || !$app['logo']): ?>hidden<?php endif; ?> h-full w-full bg-cover bg-center bg-no-repeat" <?php if($isEdit && $app['logo']): ?>style="background-image: url('//i.cdn.yimenapp.com/<?php echo htmlentities((string) $app['logo']); ?>')"<?php endif; ?>></div>
                            </div>
                            <input type="file" id="logoFile" accept="image/*" class="hidden">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 应用设置 -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                    <i class="iconfont icon-setting text-indigo-500 mr-2"></i>应用设置
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-xs font-medium text-gray-700 mb-2">
                            应用类型 <span class="text-red-500">*</span>
                        </label>
                        <div class="flex space-x-5 mt-1">
                            <label class="inline-flex items-center cursor-pointer group">
                                <div class="relative">
                                    <input type="radio" name="isPrivate" value="0" <?php if(!$isEdit || ($isEdit && $app['isPrivate'] == 0)): ?>checked<?php endif; ?>
                                        class="peer sr-only">
                                    <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                </div>
                                <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">公有应用</span>
                            </label>
                            <label class="inline-flex items-center cursor-pointer group">
                                <div class="relative">
                                    <input type="radio" name="isPrivate" value="1" <?php if($isEdit && $app['isPrivate'] == 1): ?>checked<?php endif; ?>
                                        class="peer sr-only">
                                    <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                </div>
                                <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">私有应用</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label class="block text-xs font-medium text-gray-700 mb-2">
                            上线状态 <span class="text-red-500">*</span>
                        </label>
                        <div class="flex space-x-5 mt-1">
                            <label class="inline-flex items-center cursor-pointer group">
                                <div class="relative">
                                    <input type="radio" name="isOnline" value="0" <?php if(!$isEdit || ($isEdit && $app['isOnline'] == 0)): ?>checked<?php endif; ?>
                                        class="peer sr-only">
                                    <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                </div>
                                <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">未上线</span>
                            </label>
                            <label class="inline-flex items-center cursor-pointer group">
                                <div class="relative">
                                    <input type="radio" name="isOnline" value="1" <?php if($isEdit && $app['isOnline'] == 1): ?>checked<?php endif; ?>
                                        class="peer sr-only">
                                    <div class="w-4 h-4 bg-white border border-gray-300 rounded-full
                                        group-hover:border-indigo-400 peer-checked:border-indigo-600
                                        peer-checked:border-3 transition-all duration-200"></div>
                                </div>
                                <span class="ml-1.5 text-xs text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">已上线</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高级配置 -->
            <div>
                <h2 class="text-lg font-semibold text-gray-800 pb-2 mb-4 border-b border-gray-200 flex items-center">
                    <i class="iconfont icon-code text-indigo-500 mr-2"></i>高级配置
                </h2>
                <div class="grid grid-cols-1 gap-5">
                    <div>
                        <label for="configSchema" class="block text-xs font-medium text-gray-700 mb-1">应用级配置表单 (JSON)</label>
                        <div class="relative">
                            <div class="absolute top-1.5 left-3 text-gray-400 text-xs">{ }</div>
                            <textarea id="configSchema" name="configSchema" rows="3"
                                class="w-full pl-8 pr-3 py-1.5 rounded-md border border-gray-300 shadow-sm
                                focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                hover:border-indigo-300 transition-colors duration-200 font-mono text-xs"><?php if($isEdit && $app['configSchema']): ?><?php echo !empty($app['configSchema']) ? json_encode($app['configSchema'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)  :  ''; ?><?php endif; ?></textarea>
                            <button type="button" id="openFormDesigner"
                                class="absolute right-2 top-1.5 px-2 py-1 text-xs bg-indigo-600 text-white rounded
                                hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                                focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                                <i class="iconfont icon-form mr-1"></i> 设计器
                            </button>
                        </div>
                    </div>

                    <div>
                        <label for="extra" class="block text-xs font-medium text-gray-700 mb-1">扩展配置 (JSON)</label>
                        <div class="relative">
                            <div class="absolute top-1.5 left-3 text-gray-400 text-xs">{ }</div>
                            <textarea id="extra" name="extra" rows="3"
                                class="w-full pl-8 pr-3 py-1.5 rounded-md border border-gray-300 shadow-sm
                                focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50
                                hover:border-indigo-300 transition-colors duration-200 font-mono text-xs"><?php if($isEdit && $app['extra']): ?><?php echo !empty($app['extra']) ? json_encode($app['extra'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)  :  ''; ?><?php endif; ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <button type="button" onclick="window.location.href='<?php echo url('app/index'); ?>'"
                    class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                    hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                    focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                    <i class="iconfont icon-close mr-1"></i> 取消
                </button>
                <button type="button" onclick="submitForm()"
                    class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                    hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                    focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                    <i class="iconfont icon-save mr-1"></i> 保存应用
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 表单设计器弹窗 -->
<div id="formDesignerModal" class="form-designer-modal">
    <div class="form-designer-container">
        <div class="form-designer-header">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="iconfont icon-form text-indigo-500 mr-2"></i>表单设计器
            </h3>
            <button type="button" id="closeFormDesigner" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                <i class="iconfont icon-close text-lg"></i>
            </button>
        </div>
        <div class="form-designer-body">
            <div id="form-designer-container"></div>
        </div>
        <div class="form-designer-footer">
            <button type="button" id="cancelFormDesign" class="px-4 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md border border-gray-300
                hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2
                focus:ring-offset-1 focus:ring-gray-400 flex items-center">
                <i class="iconfont icon-close mr-1"></i> 取消
            </button>
            <button type="button" id="saveFormDesign" class="px-4 py-1.5 text-xs bg-indigo-600 text-white rounded-md shadow-sm
                hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2
                focus:ring-offset-1 focus:ring-indigo-500 flex items-center">
                <i class="iconfont icon-save mr-1"></i> 保存设计
            </button>
        </div>
    </div>
</div>

    </div>

    <!-- 底部 -->
    <footer class="bg-white shadow mt-8 py-4">
    <div class="container mx-auto px-4">
        <div class="text-center text-gray-500 text-sm">
            &copy; 2025 应用市场管理系统 
        </div>
    </div>
</footer>


    <script src="/static/js/app.js"></script>
    
<!-- 引入form-create设计器依赖 -->
<script src="https://unpkg.com/vue@3"></script>
<script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
<script src="https://unpkg.com/@form-create/element-ui@next/dist/form-create.min.js"></script>
<script src="https://unpkg.com/@form-create/designer@next/dist/index.umd.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 添加输入框焦点效果
    const inputFields = document.querySelectorAll('input[type="text"], textarea, select');
    inputFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.group')?.classList.add('is-focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.group')?.classList.remove('is-focused');
        });
    });

    // 为必填字段添加验证
    const requiredFields = document.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });
    });

    // 徽标上传功能
    setupLogoUploader();

    // 初始化表单设计器
    initFormDesigner();
});

function setupLogoUploader() {
    const logoPreview = document.getElementById('logoPreview');
    const logoInput = document.getElementById('logo');
    const fileInput = document.getElementById('logoFile');
    const logoEmpty = logoPreview.querySelector('.logo-empty');
    const logoImage = logoPreview.querySelector('.logo-image');

    // 如果已有徽标，添加删除按钮
    if (logoInput.value) {
        addRemoveButton();
    }

    // 点击上传区域触发文件选择
    logoPreview.addEventListener('click', () => {
        fileInput.click();
    });

    // 处理拖拽上传
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        logoPreview.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // 拖拽效果
    ['dragenter', 'dragover'].forEach(eventName => {
        logoPreview.addEventListener(eventName, () => {
            logoPreview.classList.add('border-indigo-500');
            logoPreview.classList.add('bg-indigo-50');
        });
    });

    ['dragleave', 'drop'].forEach(eventName => {
        logoPreview.addEventListener(eventName, () => {
            logoPreview.classList.remove('border-indigo-500');
            logoPreview.classList.remove('bg-indigo-50');
        });
    });

    // 处理文件拖放
    logoPreview.addEventListener('drop', handleDrop);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        if (files.length) {
            handleFiles(files);
        }
    }

    // 处理文件选择
    fileInput.addEventListener('change', function() {
        if (this.files.length) {
            handleFiles(this.files);
        }
    });

    // 存储选择的文件
    let selectedLogoFile = null;

    function handleFiles(files) {
        const file = files[0];
        if (!file.type.match('image.*')) {
            showNotification('请选择图片文件', 'error');
            return;
        }

        // 存储选择的文件，以便稍后上传
        selectedLogoFile = file;

        // 显示文件预览
        const reader = new FileReader();
        reader.onload = function(e) {
            // 显示预览图
            logoImage.style.backgroundImage = `url(${e.target.result})`;
            logoEmpty.classList.add('hidden');
            logoImage.classList.remove('hidden');

            // 添加删除按钮
            addRemoveButton();
        };
        reader.readAsDataURL(file);
    }

    // 上传文件到服务器获取URL
    function uploadFile(file) {
        return new Promise((resolve, reject) => {
            // 显示上传中状态
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center';
            loadingOverlay.id = 'logoLoadingOverlay';
            loadingOverlay.innerHTML = '<i class="iconfont icon-loading animate-spin text-indigo-500 text-xl"></i>';
            logoPreview.appendChild(loadingOverlay);

            const formData = new FormData();
            formData.append('Logo', file);

            // 发送到上传接口
            fetch('/developer/xapi/market/?want=saveLogo&appId=<?php echo htmlentities((string) $userId); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 移除加载状态
                const loadingOverlay = document.getElementById('logoLoadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.remove();
                }

                if (data.e === 0 && data.d) {
                    // 设置隐藏输入值为返回的URL
                    logoInput.value = data.d;
                    resolve(data.d);
                } else {
                    showNotification(data.m || '徽标上传失败', 'error');
                    reject(new Error(data.m || '徽标上传失败'));
                }
            })
            .catch((error) => {
                console.error('Upload error:', error);
                // 移除加载状态
                const loadingOverlay = document.getElementById('logoLoadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.remove();
                }

                showNotification('徽标上传失败，请重试', 'error');
                reject(error);
            });
        });
    }

    // 添加删除按钮
    function addRemoveButton() {
        // 检查是否已存在删除按钮
        if (document.getElementById('logoRemoveBtn')) {
            return;
        }

        const removeBtn = document.createElement('button');
        removeBtn.id = 'logoRemoveBtn';
        removeBtn.type = 'button';
        removeBtn.className = 'absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shadow-sm hover:bg-red-600 focus:outline-none';
        removeBtn.innerHTML = '<i class="iconfont icon-close text-xs"></i>';
        removeBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            resetLogoUploader();
        });

        logoPreview.appendChild(removeBtn);
    }

    // 重置上传器状态
    function resetLogoUploader() {
        // 清除预览图
        logoImage.style.backgroundImage = '';
        logoImage.classList.add('hidden');
        logoEmpty.classList.remove('hidden');

        // 清除输入值和选择的文件
        logoInput.value = '';
        selectedLogoFile = null;

        // 移除删除按钮
        const removeBtn = document.getElementById('logoRemoveBtn');
        if (removeBtn) {
            removeBtn.remove();
        }
    }

    // 将上传文件函数暴露给全局，以便表单提交时使用
    window.uploadLogoFile = function() {
        if (selectedLogoFile) {
            return uploadFile(selectedLogoFile);
        }
        return Promise.resolve();
    };

    // 将重置函数暴露给全局
    window.resetLogoUploader = resetLogoUploader;
}

function validateField(field) {
    const fieldContainer = field.closest('.group') || field.parentNode;
    const errorMsg = fieldContainer.querySelector('.error-message');

    if (!field.value.trim()) {
        if (!errorMsg) {
            const message = document.createElement('p');
            message.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
            message.textContent = '此字段不能为空';
            fieldContainer.appendChild(message);
        }
        field.classList.add('border-red-500');
        return false;
    } else {
        if (errorMsg) {
            errorMsg.remove();
        }
        field.classList.remove('border-red-500');
        return true;
    }
}

function validateForm() {
    const requiredFields = document.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    return isValid;
}

async function submitForm() {
    if (!validateForm()) {
        // 滚动到第一个错误字段
        const firstError = document.querySelector('.error-message');
        if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return;
    }

    // 显示加载状态
    const saveButton = document.querySelector('button[onclick="submitForm()"]');
    const originalButtonText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="iconfont icon-loading animate-spin mr-1"></i> 保存中...';
    saveButton.disabled = true;

    try {
        // 检查是否需要上传徽标
        const logoInput = document.getElementById('logo');
        const logoPreview = document.getElementById('logoPreview');

        // 如果选择了新文件但还没有上传
        if (window.uploadLogoFile) {
            try {
                // 上传徽标
                await window.uploadLogoFile();
            } catch (error) {
                showNotification('徽标上传失败，请重试', 'error');
                resetButton();
                return;
            }
        }

        // 检查徽标是否已上传
        if (!logoInput.value) {
            showNotification('请上传应用徽标', 'error');
            resetButton();
            logoPreview.scrollIntoView({ behavior: 'smooth', block: 'center' });
            // 添加闪烁效果提示用户
            logoPreview.classList.add('border-red-500');
            setTimeout(() => {
                logoPreview.classList.remove('border-red-500');
            }, 2000);
            return;
        }

        const form = document.getElementById('appForm');
        const formData = new FormData(form);

        // 处理JSON字段
        const configSchema = formData.get('configSchema');
        const extra = formData.get('extra');

        if (configSchema && configSchema.trim() !== '') {
            try {
                JSON.parse(configSchema);
            } catch (e) {
                showNotification('应用级配置表单不是有效的JSON格式', 'error');
                resetButton();
                return;
            }
        }

        if (extra && extra.trim() !== '') {
            try {
                JSON.parse(extra);
            } catch (e) {
                showNotification('扩展配置不是有效的JSON格式', 'error');
                resetButton();
                return;
            }
        }

        // 根据是否为编辑模式决定提交URL
        const isEdit = <?php echo !empty($isEdit) ? 'true'  :  'false'; ?>;
        const submitUrl = isEdit
            ? '<?php echo url("app/update"); ?>?appId=<?php echo !empty($isEdit) ? htmlentities((string) $app['appId']) :  ""; ?>'
            : '<?php echo url("app/save"); ?>';

        // 发送请求
        const response = await fetch(submitUrl, {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.code === 1) {
            showNotification(data.msg, 'success');
            setTimeout(() => {
                // 根据是否为编辑模式决定跳转URL
                const redirectUrl = isEdit
                    ? '<?php echo url("app/detail"); ?>?appId=<?php echo !empty($isEdit) ? htmlentities((string) $app['appId']) :  ""; ?>'
                    : '<?php echo url("app"); ?>';
                window.location.href = redirectUrl;
            }, 1000);
        } else {
            showNotification(data.msg || '保存失败', 'error');
            resetButton();
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('保存失败，请重试', 'error');
        resetButton();
    }

    function resetButton() {
        saveButton.innerHTML = originalButtonText;
        saveButton.disabled = false;
    }
}

// 初始化表单设计器
function initFormDesigner() {
    // 获取相关DOM元素
    const modal = document.getElementById('formDesignerModal');
    const openBtn = document.getElementById('openFormDesigner');
    const closeBtn = document.getElementById('closeFormDesigner');
    const cancelBtn = document.getElementById('cancelFormDesign');
    const saveBtn = document.getElementById('saveFormDesign');
    const configSchemaInput = document.getElementById('configSchema');
    const designerContainer = document.getElementById('form-designer-container');

    let designer = null;

    // 打开设计器
    openBtn.addEventListener('click', function() {
        modal.classList.add('active');

        // 初始化设计器
        if (!designer) {
            // 创建设计器实例
            const app = Vue.createApp({
                data() {
                    return {};
                },
                template: '<fc-designer ref="designer" />'
            });

            // 注册设计器组件
            app.use(ElementPlus);
            app.use(formCreate);
            app.use(FcDesigner);

            // 挂载设计器
            const vm = app.mount(designerContainer);
            designer = vm.$refs.designer;

            // 如果已有配置数据，则导入
            const existingConfig = configSchemaInput.value.trim();
            if (existingConfig) {
                try {
                    const configData = JSON.parse(existingConfig);
                    designer.setRule(configData);
                } catch (e) {
                    console.error('解析现有配置失败:', e);
                    showNotification('解析现有配置失败，将创建新表单', 'error');
                }
            }
        }
    });

    // 关闭设计器
    function closeDesigner() {
        modal.classList.remove('active');
    }

    closeBtn.addEventListener('click', closeDesigner);
    cancelBtn.addEventListener('click', closeDesigner);

    // 保存设计
    saveBtn.addEventListener('click', function() {
        if (designer) {
            try {
                // 获取设计器生成的规则
                const rule = designer.getRule();

                // 将规则转换为JSON字符串并格式化
                const jsonStr = JSON.stringify(rule, null, 2);

                // 更新输入框的值
                configSchemaInput.value = jsonStr;

                // 关闭设计器
                closeDesigner();

                // 显示成功通知
                showNotification('表单设计已保存', 'success');
            } catch (e) {
                console.error('保存表单设计失败:', e);
                showNotification('保存表单设计失败', 'error');
            }
        }
    });

    // 点击模态框背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeDesigner();
        }
    });
}

function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.getElementById('notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'notification';
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    // 添加图标
    const icon = document.createElement('i');
    icon.className = `iconfont mr-2 ${
        type === 'success' ? 'icon-check-circle' :
        type === 'error' ? 'icon-close-circle' :
        'icon-info-circle'
    }`;
    notification.appendChild(icon);

    // 添加消息文本
    const text = document.createElement('span');
    text.textContent = message;
    notification.appendChild(text);

    // 添加到页面
    document.body.appendChild(notification);

    // 动画效果
    setTimeout(() => {
        notification.classList.add('translate-y-2', 'opacity-100');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('translate-y-2', 'opacity-100');
        notification.classList.add('-translate-y-2', 'opacity-0');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>

<style>
/* 添加动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 输入框焦点效果 */
.group.is-focused label {
    color: #4f46e5; /* indigo-600 */
}

/* 表单元素过渡效果 */
input, select, textarea {
    transition: all 0.2s ease-in-out;
}

/* 修复按钮样式 */
button[onclick="window.location.href='<?php echo url('app/index'); ?>\'"],
button[onclick="submitForm()"] {
    transition: all 0.2s ease-in-out;
}

/* 徽标上传样式 */
.logo-uploader {
    position: relative;
}

.logo-preview {
    position: relative;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.logo-preview:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.logo-image {
    position: relative;
}

.logo-image::after {
    content: '点击更换';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.5);
    color: white;
    font-size: 10px;
    padding: 2px 0;
    text-align: center;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.logo-image:hover::after {
    opacity: 1;
}

/* 徽标上传错误状态 */
.logo-preview.border-red-500 {
    animation: logoErrorPulse 1s ease-in-out;
}

@keyframes logoErrorPulse {
    0%, 100% { border-color: #ef4444; }
    50% { border-color: #f87171; }
}

/* 徽标删除按钮 */
#logoRemoveBtn {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease-in-out;
}

.logo-preview:hover #logoRemoveBtn {
    opacity: 1;
    transform: scale(1);
}
</style>

</body>
</html>
