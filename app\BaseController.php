<?php
declare (strict_types = 1);

namespace app;

use think\App;
use think\exception\ValidateException;
use think\Validate;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {}

    /**
     * 验证数据
     * @access protected
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 返回成功的JSON响应
     * @param string $msg 成功消息
     * @param array $data 返回数据
     * @param int $code 状态码，默认为1
     * @return \think\Response
     */
    protected function success(string $msg = '操作成功', array $data = [], int $code = 1)
    {
        $result = [
            'code' => $code,
            'msg' => $msg,
        ];

        if (!empty($data)) {
            $result['data'] = $data;
        }

        return json($result);
    }

    /**
     * 返回失败的JSON响应
     * @param string $msg 错误消息
     * @param array $data 返回数据
     * @param int $code 状态码，默认为0
     * @return \think\Response
     */
    protected function error(string $msg = '操作失败', array $data = [], int $code = 0)
    {
        $result = [
            'code' => $code,
            'msg' => $msg,
        ];

        if (!empty($data)) {
            $result['data'] = $data;
        }

        return json($result);
    }

    /**
     * 返回JSON响应
     * @param int $code 状态码
     * @param string $msg 消息
     * @param array $data 返回数据
     * @return \think\Response
     */
    protected function jsonResponse(int $code, string $msg, array $data = [])
    {
        $result = [
            'code' => $code,
            'msg' => $msg,
        ];

        if (!empty($data)) {
            $result['data'] = $data;
        }

        return json($result);
    }

}
