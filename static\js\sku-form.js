/**
 * SKU表单管理器
 */
const SkuForm = {
    config: {
        isEdit: false,
        appId: '',
        appSkuId: '',
        submitUrl: '',
        indexUrl: ''
    },

    /**
     * 初始化
     */
    init(config) {
        this.config = { ...this.config, ...config };
        
        // 初始化表单元素
        this.initFormElements();
        
        // 初始化运行时环境选择
        this.initRuntimeSelection();
    },

    /**
     * 初始化表单元素
     */
    initFormElements() {
        // 添加输入框焦点效果
        const inputFields = document.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
        inputFields.forEach(field => {
            field.addEventListener('focus', function () {
                this.closest('.form-group')?.classList.add('is-focused');
            });

            field.addEventListener('blur', function () {
                this.closest('.form-group')?.classList.remove('is-focused');

                // 简单验证（如果是必填字段）
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('border-red-500');

                    // 添加错误提示
                    const fieldGroup = this.closest('.form-group');
                    if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                        const errorMsg = document.createElement('p');
                        errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                        errorMsg.textContent = '此字段不能为空';
                        fieldGroup.appendChild(errorMsg);
                    }
                } else {
                    this.classList.remove('border-red-500');

                    // 移除错误提示
                    const fieldGroup = this.closest('.form-group');
                    const errorMsg = fieldGroup?.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                }
            });
        });

        // 价格输入框特殊处理
        const priceInputs = document.querySelectorAll('input[type="number"][step="0.01"]');
        priceInputs.forEach(input => {
            input.addEventListener('blur', function () {
                if (this.value) {
                    this.value = parseFloat(this.value).toFixed(2);
                }
            });
        });
    },

    /**
     * 初始化运行时环境选择
     */
    initRuntimeSelection() {
        const checkboxes = document.querySelectorAll('input[name="runtimeIds[]"]');
        const countElement = document.querySelector('#selectedRuntimesCount span');

        // 更新选中数量
        const updateSelectedCount = () => {
            const selectedCount = document.querySelectorAll('input[name="runtimeIds[]"]:checked').length;
            countElement.textContent = selectedCount;
        };

        // 添加事件监听器
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        // 初始更新
        updateSelectedCount();
    },

    /**
     * 验证表单
     */
    validateForm() {
        const form = document.getElementById('skuForm');
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        // 验证必填字段
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-red-500');

                // 添加抖动效果
                field.classList.add('animate-shake');
                setTimeout(() => {
                    field.classList.remove('animate-shake');
                }, 600);

                // 添加错误提示
                const fieldGroup = field.closest('.form-group');
                if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                    const errorMsg = document.createElement('p');
                    errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                    errorMsg.textContent = '此字段不能为空';
                    fieldGroup.appendChild(errorMsg);
                }

                isValid = false;
            }
        });

        // 检查是否选择了运行时环境
        const runtimeIds = document.querySelectorAll('input[name="runtimeIds[]"]:checked');
        if (runtimeIds.length === 0) {
            const runtimesContainer = document.querySelector('.p-4.border.border-gray-200.rounded-md.bg-gray-50');
            runtimesContainer.classList.add('border-red-500', 'animate-shake');

            setTimeout(() => {
                runtimesContainer.classList.remove('animate-shake');
            }, 600);

            // 添加错误提示
            const fieldGroup = runtimesContainer.closest('.form-group');
            if (fieldGroup && !fieldGroup.querySelector('.error-message')) {
                const errorMsg = document.createElement('p');
                errorMsg.className = 'error-message text-red-500 text-xs mt-1 animate-fadeIn';
                errorMsg.textContent = '请至少选择一个运行时环境';
                fieldGroup.appendChild(errorMsg);
            }

            isValid = false;
        }

        // 如果有错误，滚动到第一个错误字段
        if (!isValid) {
            const firstErrorField = form.querySelector('.border-red-500');
            if (firstErrorField) {
                firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                if (firstErrorField.tagName !== 'DIV') {
                    firstErrorField.focus();
                }
            }
        }

        return isValid;
    },

    /**
     * 提交表单
     */
    async submitForm() {
        // 验证表单
        if (!this.validateForm()) {
            this.showNotification('表单验证失败，请检查输入', 'error');
            return;
        }

        // 显示加载状态
        const submitButton = document.getElementById('submitButton');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="iconfont icon-loading animate-spin mr-1"></i> 保存中...';
        submitButton.disabled = true;

        try {
            const form = document.getElementById('skuForm');
            const formData = new FormData(form);

            // 发送请求
            const response = await fetch(this.config.submitUrl, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.code === 1) {
                this.showNotification(data.msg, 'success');
                setTimeout(() => {
                    window.location.href = this.config.indexUrl;
                }, 1000);
            } else {
                this.showNotification(data.msg || '保存失败', 'error');
                this.resetButton(submitButton, originalButtonText);
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNotification('保存失败，请重试', 'error');
            this.resetButton(submitButton, originalButtonText);
        }
    },

    /**
     * 重置按钮状态
     */
    resetButton(button, originalText) {
        button.innerHTML = originalText;
        button.disabled = false;
    },

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 移除现有通知
        const existingNotification = document.getElementById('notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = 'notification';
        notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 ease-in-out flex items-center ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;

        // 添加图标
        const icon = document.createElement('i');
        icon.className = `iconfont mr-2 ${
            type === 'success' ? 'icon-check-circle' :
            type === 'error' ? 'icon-close-circle' :
            'icon-info-circle'
        }`;
        notification.appendChild(icon);

        // 添加消息文本
        const text = document.createElement('span');
        text.textContent = message;
        notification.appendChild(text);

        // 添加到页面
        document.body.appendChild(notification);

        // 动画效果
        setTimeout(() => {
            notification.classList.add('translate-y-2', 'opacity-100');
        }, 10);

        // 自动关闭
        setTimeout(() => {
            notification.classList.remove('translate-y-2', 'opacity-100');
            notification.classList.add('-translate-y-2', 'opacity-0');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
};

// 全局函数，供模板调用
function submitForm() {
    SkuForm.submitForm();
}
